/**
 * RequestForInformationTool
 *
 * This tool is used by agents to request additional information from users
 * when they don't have enough context or documents to provide a meaningful response.
 * It generates a structured request for information that can be presented to the user.
 */

import { processWithGroq } from './groq-ai';
import { processWithAnthropic } from './anthropic-ai';
import { processWithGoogleAI } from './google-ai';
import { RequestForInformationSchema, safeParseJson } from '../schemas/llmResponseSchemas';

export interface RequestForInformationInput {
  query: string;
  missingContext?: string[];
  category?: string;
  attemptedSearches?: string[];
  userId?: string;
}

export interface RequestForInformationResult {
  success: boolean;
  requestMessage: string;
  specificQuestions: string[];
  recommendedActions: string[];
  error?: string;
}

export class RequestForInformationTool {
  /**
   * Generate a request for information when the agent doesn't have enough context
   * @param input The input parameters
   * @returns A structured request for information
   */
  async process(input: RequestForInformationInput): Promise<RequestForInformationResult> {
    try {
      console.log(`RequestForInformationTool: Processing request for more information about "${input.query}"`);

      // Create a prompt for the LLM to generate a request for information
      const prompt = this.createPrompt(input);

      // Try with Claude first, then fall back to other models if needed
      let response: any;
      try {
        // First try with Claude
        const claudeResponse = await processWithAnthropic({
          prompt,
          model: "claude-sonnet-4-0-latest",
          modelOptions: {
            temperature: 0.3
          }
        });

        // Parse the JSON response
        response = this.extractJsonFromResponse(claudeResponse);
      } catch (claudeError) {
        console.error("Error with Claude in RequestForInformationTool, falling back to Groq:", claudeError);

        try {
          // Fall back to Groq
          const groqResponse = await processWithGroq({
            prompt,
            model: "llama-3.3-70b-versatile",
            modelOptions: {
              temperature: 0.3,
              maxTokens: 2000
            }
          });

          // Parse the JSON response
          response = this.extractJsonFromResponse(groqResponse);
        } catch (groqError) {
          console.error("Error with Groq in RequestForInformationTool, falling back to Gemini:", groqError);

          // Fall back to Gemini as last resort
          const geminiResponse = await processWithGoogleAI({
            prompt,
            model: "gemini-2.5-flash"
          });

          // Parse the JSON response
          response = this.extractJsonFromResponse(geminiResponse);
        }
      }

      return {
        success: true,
        requestMessage: response.requestMessage || "I need more information to provide a comprehensive response.",
        specificQuestions: response.specificQuestions || [],
        recommendedActions: response.recommendedActions || []
      };
    } catch (error) {
      console.error("Error in RequestForInformationTool:", error);
      return {
        success: false,
        requestMessage: "I need more specific information about your request.",
        specificQuestions: ["Could you provide more details about what you're looking for?"],
        recommendedActions: ["Please provide more context about your request."],
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Create a prompt for the LLM to generate a request for information
   * @param input The input parameters
   * @returns A prompt for the LLM
   */
  private createPrompt(input: RequestForInformationInput): string {
    return `
You are an expert assistant tasked with requesting additional information from a user when there is insufficient context to provide a meaningful response.

Original Query: "${input.query}"

${input.category ? `Category: ${input.category}` : ''}

${input.missingContext && input.missingContext.length > 0 ?
  `Missing Context:
${input.missingContext.map(ctx => `- ${ctx}`).join('\n')}` :
  'No specific context is available for this query.'}

${input.attemptedSearches && input.attemptedSearches.length > 0 ?
  `Attempted Searches:
${input.attemptedSearches.map(search => `- ${search}`).join('\n')}` :
  'No document searches have been attempted yet.'}

Based on the above information, please generate:
1. A polite and helpful message requesting more information from the user
2. 3-5 specific questions that would help gather the missing information
3. 2-3 recommended actions the user could take to provide better context

Format your response as a JSON object with the following structure:
{
  "requestMessage": "A polite and helpful message explaining why more information is needed",
  "specificQuestions": ["Question 1", "Question 2", "Question 3", ...],
  "recommendedActions": ["Action 1", "Action 2", ...]
}

Ensure your response is ONLY the JSON object with no additional text, markdown formatting, or code blocks.
`;
  }

  /**
   * Extract JSON from a potentially formatted response using Zod schema
   * @param response The response from the LLM
   * @returns The parsed and validated JSON object
   */
  private extractJsonFromResponse(response: string): any {
    // Use the safeParseJson function with our Zod schema
    return safeParseJson(RequestForInformationSchema, response, {
      requestMessage: "I need more information to provide a comprehensive response.",
      specificQuestions: [
        "Could you provide more details about what you're looking for?",
        "What specific aspects of this topic are you most interested in?",
        "Do you have any existing documents or resources you can share?"
      ],
      recommendedActions: [
        "Share any existing marketing materials or documents",
        "Provide information about your business objectives",
        "Specify your target audience and preferred content formats"
      ]
    });
  }
}

// Export a singleton instance for easy import
export const requestForInformationTool = new RequestForInformationTool();
