/**
 * Anthropic integration for the LLM tool
 *
 * This module provides functions to interact with Anthropic's Claude models
 * through their official SDK. It handles message creation, streaming responses,
 * error management, and provides access to available models.
 */

// Import the official Anthropic SDK
import Anthropic from '@anthropic-ai/sdk';

/**
 * Error class for Anthropic API-specific errors
 */
class AnthropicAPIError extends Error {
  statusCode: number;
  requestId?: string;

  constructor(message: string, statusCode: number, requestId?: string) {
    super(message);
    this.name = "AnthropicAPIError";
    this.statusCode = statusCode;
    this.requestId = requestId;
  }
}

// Define interfaces for Anthropic models and responses
export interface AnthropicModel {
  id: string;
  name: string;
  contextWindow: number;
  capabilities: string[];
  platform: string;
}

export interface AnthropicCloudModel {
  id: string;
  name: string;
  platform: string;
}

export interface AnthropicMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

export interface AnthropicProcessingOptions {
  prompt: string;
  model?: string;
  max_tokens?: number;
  temperature?: number;
  messages?: AnthropicMessage[];
  modelOptions?: Record<string, any>;
  stream?: boolean;
  onProgress?: (delta: string, fullContent: string) => void;
  apiKey?: string;
}

/**
 * Create an Anthropic client instance with the provided API key
 * @param apiKey - The Anthropic API key (defaults to process.env["ANTHROPIC_API_KEY"])
 * @returns The initialized Anthropic client
 */
export function createAnthropicClient(apiKey?: string): Anthropic {
  // Use the provided API key or fall back to environment variable
  const key = apiKey || process.env.CLAUDE_API_KEY || '************************************************************************************************************'
  if (!key) {
    console.error('No Anthropic API key provided. Please set ANTHROPIC_API_KEY environment variable or pass apiKey parameter.');
  }

  return new Anthropic({
    apiKey: key,
  });
}

/**
 * Maps simplified model names to their full API identifiers
 * This is the core mapping function that resolves UI-friendly names to valid API model IDs
 *
 * @param modelName - The simplified model name or full model ID
 * @returns The full model ID for API use
 */
export function resolveModelIdentifier(modelName: string): string {
  // If the model already includes a version suffix, assume it's already a valid ID
  if (modelName.includes('-2024') || modelName.includes('-2025') || modelName.includes('-latest')) {
    return modelName;
  }

  // Define mapping for simplified model names to their latest version identifiers
  const modelMap: Record<string, string> = {
    // Claude 4 models
    'claude-sonnet-4-0': 'claude-sonnet-4-20250514',
    'claude-sonnet-4-20250514': 'claude-sonnet-4-20250514',
    'claude-opus-4-0': 'claude-opus-4-0',
    'claude-4': 'claude-sonnet-4-20250514',  // Default to Sonnet for generic Claude 4
  };

  // Return the mapped ID or the original if no mapping exists
  return modelMap[modelName.toLowerCase()] || modelName;
}

/**
 * Process content with Anthropic
 * @param options - Processing options
 * @param options.prompt - The prompt to send to the LLM
 * @param options.model - The model to use (default: "claude-3-5-sonnet-latest")
 * @param options.max_tokens - Maximum number of tokens to generate (default: 1024)
 * @param options.temperature - Sampling temperature (default: 0.7)
 * @param options.messages - Array of message objects with role and content (optional)
 * @param options.modelOptions - Additional model-specific options including:
 *   - top_k: Limits sampling to the K most likely tokens
 *   - top_p: Limits sampling to tokens with cumulative probability P
 *   - system: System prompt to control Claude's behavior
 *   - tools: Array of tools available to the model
 *   - tool_choice: Control which tool the model uses
 * @param options.stream - Whether to stream the response (default: false)
 * @param options.onProgress - Callback for streaming progress
 * @param options.apiKey - Custom API key (defaults to process.env["ANTHROPIC_API_KEY"])
 * @returns The generated message or content
 */
export async function processWithAnthropic(options: AnthropicProcessingOptions): Promise<string> {
  try {
    const {
      prompt,
      model = "claude-sonnet-4-20250514",
      max_tokens = 7024,
      temperature = 0.7,
      messages,
      modelOptions = {},
      stream = false,
      onProgress = null,
      apiKey = process.env.ANTHROPIC_API_KEY || process.env.CLAUDE_API_KEY
    } = options;

    // Resolve the model identifier from simplified name to full API ID
    const resolvedModel = resolveModelIdentifier(model);

    // Log the model resolution for debugging
    console.log(`Processing with Anthropic: ${model} → ${resolvedModel}`);

    // Validate that the resolved model exists in our known model list
    if (!isModelAvailable(resolvedModel)) {
      console.warn(`Model "${resolvedModel}" is not in the known model list. This may cause API errors.`);
    }

    // Initialize the Anthropic client
    const anthropic = createAnthropicClient(apiKey);

    // Prepare formatted messages
    const formattedMessages = messages || [
      { role: "user", content: prompt }
    ];

    // Prepare request parameters with the resolved model ID
    // Remove maxTokens from modelOptions to avoid conflicts with max_tokens
    const { maxTokens, ...otherModelOptions } = modelOptions || {};

    const requestParams = {
      model: resolvedModel,
      max_tokens: max_tokens,
      messages: formattedMessages,
      temperature: temperature,
      ...otherModelOptions
    };

    if (stream) {
      // Stream the response
      const streamResponse = await anthropic.messages.create({
        ...requestParams,
        stream: true
      } as any);

      let fullContent = "";

      // Process the stream
      for await (const chunk of streamResponse as any) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text') {
          const textDelta = chunk.delta.text;
          fullContent += textDelta;

          if (onProgress && typeof onProgress === 'function') {
            onProgress(textDelta, fullContent);
          }
        }
      }

      return fullContent.trim();
    } else {
      // Get a complete response
      const response = await anthropic.messages.create(requestParams as any);

      // Extract the text content from the response
      let content = "";
      if (response.content && response.content.length > 0) {
        for (const block of response.content) {
          if (block.type === 'text') {
            content += block.text;
          }
        }
      }

      return content.trim();
    }
  } catch (error: any) {
    // Enhanced error handling for the Anthropic SDK
    if (error.status === 404 && error.error?.type === 'not_found_error') {
      // Special handling for model not found errors
      const message = `Anthropic API Error: Model not found. The model "${options.model}" is not available or does not exist. Please use a valid model ID.`;

      // List available models for clarity
      const availableModels = getAnthropicModels()
        .map(model => `${model.name} (${model.id})`)
        .join('\n- ');

      throw new AnthropicAPIError(
        `${message}\n\nAvailable models:\n- ${availableModels}`,
        404,
        error.error?.request_id
      );
    } else if (error.status) {
      throw new AnthropicAPIError(
        error.message || "Unknown Anthropic API error",
        error.status,
        error.error?.request_id
      );
    }

    console.error("Error processing content with Anthropic:", error);
    throw error; // Re-throw for better error handling upstream
  }
}

/**
 * Get available Anthropic models
 * @returns List of available models with metadata
 */
export function getAnthropicModels(): AnthropicModel[] {
  return [
    // Claude 4 Models (Latest generation)
    {
      id: "claude-sonnet-4-20250514",
      name: "Claude Sonnet 4.0 (2025-05-14)",
      contextWindow: 200000,
      capabilities: ["reasoning", "tool-use", "vision"],
      platform: "anthropic-api"
    },
    {
      id: "claude-sonnet-4-0",
      name: "Claude Sonnet 4.0",
      contextWindow: 200000,
      capabilities: ["reasoning", "tool-use", "vision"],
      platform: "anthropic-api"
    },
    {
      id: "claude-opus-4-0",
      name: "Claude Opus 4.0",
      contextWindow: 200000,
      capabilities: ["reasoning", "tool-use", "vision"],
      platform: "anthropic-api"
    },

    // Claude 4.0 Models
    {
      id: "claude-sonnet-4-0-latest",
      name: "Claude 4 Sonnet (Latest)",
      contextWindow: 200000,
      capabilities: ["reasoning", "tool-use", "vision"],
      platform: "anthropic-api"
    },
    {
      id: "claude-sonnet-4-0-20240307",
      name: "Claude 4 Sonnet",
      contextWindow: 200000,
      capabilities: ["reasoning", "tool-use", "vision"],
      platform: "anthropic-api"
    },
  ];
}

/**
 * Get AWS Bedrock Anthropic models
 * @returns List of AWS Bedrock models
 */
export function getAWSBedrockModels(): AnthropicCloudModel[] {
  return [
    /*
    // 3.7 models commented out until confirmed available
    { id: "anthropic.claude-sonnet-4-0-20250219-v1:0", name: "Claude 4 Sonnet (AWS)", platform: "aws-bedrock" },
    */
    { id: "anthropic.claude-sonnet-4-0-20250514", name: "Claude 4 Sonnet (AWS)", platform: "aws-bedrock" },
    { id: "anthropic.claude-opus-4-0-20250514", name: "Claude 4 Opus (AWS)", platform: "aws-bedrock" },
    { id: "anthropic.claude-3-5-haiku-20241022-v1:0", name: "Claude 3.5 Haiku (AWS)", platform: "aws-bedrock" },
    { id: "anthropic.claude-3-5-sonnet-20241022-v2:0", name: "Claude 3.5 Sonnet v2 (AWS)", platform: "aws-bedrock" },
    { id: "anthropic.claude-3-5-sonnet-20240620-v1:0", name: "Claude 3.5 Sonnet (AWS)", platform: "aws-bedrock" },
    { id: "anthropic.claude-3-opus-20240229-v1:0", name: "Claude 3 Opus (AWS)", platform: "aws-bedrock" },
    { id: "anthropic.claude-3-sonnet-20240229-v1:0", name: "Claude 3 Sonnet (AWS)", platform: "aws-bedrock" },
    { id: "anthropic.claude-3-haiku-20240307-v1:0", name: "Claude 3 Haiku (AWS)", platform: "aws-bedrock" }
  ];
}

/**
 * Get GCP Vertex AI Anthropic models
 * @returns List of GCP Vertex AI models
 */
export function getGCPVertexAIModels(): AnthropicCloudModel[] {
  return [
    /*
    // 3.7 models commented out until confirmed available
    { id: "claude-sonnet-4-0@20250219", name: "Claude 4 Sonnet (GCP)", platform: "gcp-vertex-ai" },
    */
    { id: "claude-3-5-haiku@20241022", name: "Claude 3.5 Haiku (GCP)", platform: "gcp-vertex-ai" },
    { id: "claude-3-5-sonnet-v2@20241022", name: "Claude 3.5 Sonnet v2 (GCP)", platform: "gcp-vertex-ai" },
    { id: "claude-3-5-sonnet-v1@20240620", name: "Claude 3.5 Sonnet (GCP)", platform: "gcp-vertex-ai" },
    { id: "claude-3-opus@20240229", name: "Claude 3 Opus (GCP)", platform: "gcp-vertex-ai" },
    { id: "claude-3-sonnet@20240229", name: "Claude 3 Sonnet (GCP)", platform: "gcp-vertex-ai" },
    { id: "claude-3-haiku@20240307", name: "Claude 3 Haiku (GCP)", platform: "gcp-vertex-ai" }
  ];
}

/**
 * Get all available models across all platforms
 * @returns Combined list of all available models
 */
export function getAllModels(): (AnthropicModel | AnthropicCloudModel)[] {
  return [
    ...getAnthropicModels(),
    ...getAWSBedrockModels(),
    ...getGCPVertexAIModels()
  ];
}

/**
 * Get model information by ID or simplified name
 * @param modelId - The model ID or simplified name to look up
 * @returns Model information or null if not found
 */
export function getModelInfo(modelId: string): (AnthropicModel | AnthropicCloudModel | null) {
  // First try direct lookup
  const directMatch = getAllModels().find(model => model.id === modelId);
  if (directMatch) return directMatch;

  // If no direct match, try to resolve the model ID and search again
  const resolvedId = resolveModelIdentifier(modelId);
  return getAllModels().find(model => model.id === resolvedId) || null;
}

/**
 * Format a message for the Anthropic API
 * @param content - The message content
 * @param role - The role (user, assistant, system)
 * @returns Formatted message object
 */
export function formatMessage(content: string, role: "user" | "assistant" | "system" = "user"): AnthropicMessage {
  return { role, content };
}

/**
 * Create a messages array from a conversation history
 * @param conversationHistory - Array of message objects
 * @returns Formatted messages array for the API
 */
export function createMessagesArray(conversationHistory: AnthropicMessage[]): AnthropicMessage[] {
  return conversationHistory.map(msg => formatMessage(msg.content, msg.role));
}

/**
 * Validate if the provided model ID is available
 * @param modelId - The model ID or simplified name to validate
 * @param platform - Optional platform to check (anthropic-api, aws-bedrock, gcp-vertex-ai)
 * @returns Whether the model is available
 */
export function isModelAvailable(modelId: string, platform: string | null = null): boolean {
  // First try to resolve the model ID if it's a simplified name
  const resolvedId = resolveModelIdentifier(modelId);

  // Filter models by platform if specified
  const models = platform ?
    getAllModels().filter(model => model.platform === platform) :
    getAllModels();

  // Check if the resolved ID exists in the available models
  return models.some(model => model.id === resolvedId);
}

/**
 * Get the recommended default model
 * @returns The recommended default model ID
 */
export function getRecommendedDefaultModel(): string {
  return "claude-sonnet-4-20250514";
}

/**
 * Get simplified model names for UI display
 * @returns List of simplified model names for UI
 */
export function getSimplifiedModelNames(): { id: string, name: string }[] {
  return [
    { id: "claude-sonnet-4-20250514", name: "Claude Sonnet 4.0 (Latest)" },
    { id: "claude-sonnet-4-0", name: "Claude Sonnet 4.0" },
    { id: "claude-opus-4-0", name: "Claude Opus 4.0" },
    // { id: "claude-3-5-sonnet", name: "Claude 3.5 Sonnet" },
    // { id: "claude-3-5-haiku", name: "Claude 3.5 Haiku" },
    // { id: "claude-3-opus", name: "Claude 3 Opus" },
    // { id: "claude-3-sonnet", name: "Claude 3 Sonnet" },
    // { id: "claude-3-haiku", name: "Claude 3 Haiku" }
  ];
}
