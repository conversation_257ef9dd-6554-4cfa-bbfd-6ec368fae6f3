/**
 * Prompt Optimizer Tool
 *
 * This tool takes an original prompt and a list of criteria, then uses Claude 4.0
 * to optimize the prompt by applying those criteria.
 */

import { processWithClaude } from './claude-ai';

// Define interfaces for the tool
export interface PromptOptimizerOptions {
  originalPrompt: string;
  criteria: string[];
  includeExplanation?: boolean;
  modelOptions?: {
    temperature?: number;
    maxTokens?: number;
    [key: string]: any;
  };
}

export interface PromptOptimizerResult {
  success: boolean;
  optimizedPrompt: string;
  explanation?: string;
  error?: string;
}

export class PromptOptimizerTool {
  /**
   * Static description of the tool and its usage
   * This helps AI agents understand how to use the tool effectively
   */
  static description = {
    name: "optimizePrompt",
    description: "Optimizes a prompt by applying a list of criteria using Claude 4.0.",
    parameters: {
      type: "object",
      properties: {
        originalPrompt: {
          type: "string",
          description: "The original prompt to optimize."
        },
        criteria: {
          type: "array",
          items: {
            type: "string"
          },
          description: "List of criteria to apply for optimization."
        },
        includeExplanation: {
          type: "boolean",
          description: "Whether to include an explanation of the changes made to the prompt.",
          default: false
        },
        modelOptions: {
          type: "object",
          description: "Optional configuration for the Claude model.",
          properties: {
            temperature: {
              type: "number",
              description: "Controls randomness in the output. Lower values are more deterministic.",
              default: 0.5
            },
            maxTokens: {
              type: "number",
              description: "Maximum number of tokens to generate.",
              default: 3000
            }
          }
        }
      },
      required: ["originalPrompt", "criteria"]
    },
    returns: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          description: "Whether the operation was successful."
        },
        optimizedPrompt: {
          type: "string",
          description: "The optimized prompt."
        },
        explanation: {
          type: "string",
          description: "Explanation of the changes made to the original prompt."
        },
        error: {
          type: "string",
          description: "Error message if the operation failed."
        }
      }
    },
    examples: [
      {
        input: {
          originalPrompt: "Write a blog post about climate change",
          criteria: [
            "Specify the target audience",
            "Define the tone (formal, conversational, etc.)",
            "Indicate desired length",
            "Specify if sources should be included",
            "Clarify the perspective or stance"
          ]
        },
        output: {
          success: true,
          optimizedPrompt: "Write a 1500-word blog post about climate change targeted at environmentally-conscious millennials. Use a conversational yet informative tone, include at least 3 reputable scientific sources, and approach the topic from a solutions-oriented perspective that emphasizes individual and policy actions.",
          explanation: "Added target audience (millennials), specified length (1500 words), defined tone (conversational yet informative), requested sources (3 reputable scientific sources), and clarified perspective (solutions-oriented)."
        }
      }
    ]
  };

  /**
   * Optimize a prompt by applying criteria
   * @param options - Options for prompt optimization
   * @returns - Optimized prompt and explanation
   */
  async optimizePrompt(options: PromptOptimizerOptions): Promise<PromptOptimizerResult> {
    try {
      const {
        originalPrompt,
        criteria,
        includeExplanation = false,
        modelOptions = {}
      } = options;

      if (!originalPrompt) {
        throw new Error("Original prompt is required");
      }

      if (!criteria || !Array.isArray(criteria) || criteria.length === 0) {
        throw new Error("At least one criterion is required");
      }

      // Format criteria as a numbered list
      const formattedCriteria = criteria
        .map((criterion, index) => `${index + 1}. ${criterion}`)
        .join('\n');

      // Create a system prompt for Claude 4.0
      const systemPrompt = `
You are an expert at crafting clear and effective prompts for AI language models. I need you to optimize the following prompt to ensure it produces the highest quality response:
Please analyze the ORIGINAL PROMPT and improve it by applying the following criteria so that it:
1. Clarifies any ambiguous instructions
2. Guides the AI toward a comprehensive and well-organized response
3. Ensures it communicates all necessary context and requirements

ORIGINAL PROMPT:
"""
${originalPrompt}
"""

CRITERIA TO APPLY:
${formattedCriteria}

EXTREMELY IMPORTANT INSTRUCTIONS:
1. Your response must ONLY contain the optimized prompt itself.
2. DO NOT include any meta-instructions, notes, explanations, or commentary.
3. DO NOT include any text like "** assumes we have tools **" or similar notes.
4. DO NOT include numbered lists of teams, departments, or categories unless they are part of the actual prompt.
5. DO NOT include any headers, formatting, or additional context.
6. DO NOT include any asterisks (**) or bracketed notes like [NOTE] or [IMPORTANT].
7. DO NOT include any lines starting with "The PMOAgent will" or similar descriptions.
8. DO NOT include any lines that describe assumptions about tools, context, or implementation.
9. The optimized prompt should be ready to use directly with an AI system.
10. The optimized prompt should start with an action verb like "Provide", "Explain", "Describe", etc.

REMEMBER: I need ONLY the optimized prompt text that a user would directly copy and paste into an AI system. Nothing else.

`;

      // Log what we're sending to Claude - use console.warn for higher visibility
      console.warn("🔍 DEBUGGING - SENDING TO CLAUDE 🔍");
      console.warn("System Prompt:", systemPrompt);
      console.warn("Model:", "claude-sonnet-4-0-latest");
      console.warn("Temperature:", modelOptions.temperature !== undefined ? modelOptions.temperature : 0.5);
      console.warn("MaxTokens:", modelOptions.maxTokens !== undefined ? modelOptions.maxTokens : 3000);

      // No process.stdout statements to avoid browser compatibility issues

      // Process with Claude 4.0
      const result = await processWithClaude({
        prompt: systemPrompt,
        model: "claude-sonnet-4-0-latest", // Using Claude 4.0 as specified
        modelOptions: {
          temperature: modelOptions.temperature !== undefined ? modelOptions.temperature : 0.5,
          maxTokens: modelOptions.maxTokens !== undefined ? modelOptions.maxTokens : 3000,
          ...modelOptions
        }
      });

      // Log Claude's complete response with higher visibility
      console.warn("🔍 DEBUGGING - CLAUDE RESPONSE 🔍");
      console.warn("Response length:", result.length);
      console.warn("Response preview:", result.substring(0, 200) + "...");

      // No process.stdout statements to avoid browser compatibility issues

      // Log response details for debugging
      console.warn("Full response length:", result.length);
      console.warn("Full response first 500 chars:", result.substring(0, 500));

      // Parse the result to extract the optimized prompt and explanation if requested
      console.warn("🔍 DEBUGGING - STARTING PARSING PROCESS 🔍");
      let { optimizedPrompt, explanation } = this._parseOptimizedPromptFromResponse(result, includeExplanation);

      console.warn("🔍 DEBUGGING - PARSING RESULTS 🔍");
      console.warn("Optimized Prompt:", optimizedPrompt || "NONE");
      console.warn("Explanation:", explanation || "NONE");

      // No process.stdout statements to avoid browser compatibility issues

      // Check if we have a valid optimized prompt
      if (!optimizedPrompt) {
        console.warn("🚨 FALLBACK TRIGGERED - No optimized prompt was generated from Claude's response 🚨");

        // Add browser alert for high visibility
        try {
          if (typeof window !== 'undefined' && window.alert) {
            window.alert("DEBUG: Prompt optimizer fallback triggered - Claude response parsing failed");
          }
        } catch (e) {
          console.warn("Could not show alert:", e);
        }

        // Log the original prompt and criteria for debugging with high visibility
        console.warn("🔍 DEBUGGING - FALLBACK DETAILS 🔍");
        console.warn("Original Prompt:", originalPrompt);
        console.warn("Criteria:", criteria);

        // No process.stdout statements to avoid browser compatibility issues

        // Create a fallback optimized prompt by applying the criteria manually
        const criteriaText = criteria.map(criterion => criterion.toLowerCase()).join(', ');
        console.warn("Criteria Text for Fallback:", criteriaText);

        // Create a more sophisticated fallback that clearly shows this is an optimized prompt, not an answer
        optimizedPrompt = `Provide a detailed and well-structured response about ${originalPrompt.trim().replace(/[?.,!]$/, '')} that is ${criteriaText}. Include specific examples and reliable information.`;
        console.warn("Generated Fallback Prompt:", optimizedPrompt);

        if (includeExplanation) {
          explanation = "The AI model was unable to generate an optimized prompt, so a basic template was created by appending the criteria to the original prompt.";
          console.warn("Generated Fallback Explanation:", explanation);
        }

        // Log fallback details for debugging
        console.warn("🚨 FALLBACK DETAILS 🚨");
        console.warn("Original Prompt:", originalPrompt);
        console.warn("Criteria:", JSON.stringify(criteria, null, 2));
        console.warn("Generated Fallback Prompt:", optimizedPrompt);
        console.warn("Explanation:", explanation || "None");
      }

      return {
        success: true,
        optimizedPrompt,
        ...(includeExplanation && explanation ? { explanation } : {})
      };
    } catch (error: any) {
      console.error("Error optimizing prompt:", error);
      return {
        success: false,
        optimizedPrompt: "",
        error: error.message || "Unknown error occurred while optimizing the prompt"
      };
    }
  }

  /**
   * Parse optimized prompt and explanation from the LLM response
   * @private
   * @param response - Raw response from the LLM
   * @param includeExplanation - Whether explanation was requested
   * @returns - Object containing optimized prompt and explanation
   */
  private _parseOptimizedPromptFromResponse(
    response: string,
    includeExplanation: boolean = false
  ): { optimizedPrompt: string; explanation: string } {
    // Default values in case parsing fails
    let optimizedPrompt = "";
    let explanation = "";

    // Log first part of response for debugging
    console.log("PromptOptimizerTool: Raw response from Claude:",
      response.length > 200 ? response.substring(0, 200) + "..." : response);

    // Clean up the response - remove extra whitespace and normalize line endings
    const cleanedResponse = response
      .replace(/\r\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    console.log("PromptOptimizerTool: Cleaned response length:", cleanedResponse.length);

    // First, try to extract using explicit headers if they exist
    if (includeExplanation) {
      // Format with explanation - try to extract the optimized prompt
      // Try different patterns to match Claude's output format
      const promptPatterns = [
        /OPTIMIZED PROMPT:\s*\n([\s\S]*?)(?:\n\s*EXPLANATION:|\n\s*$)/i,
        /OPTIMIZED PROMPT:\s*([\s\S]*?)(?:\n\s*EXPLANATION:|\n\s*$)/i,
        /\[Your optimized prompt here[^\]]*\]\s*\n([\s\S]*?)(?:\n\s*EXPLANATION:|\n\s*$)/i
      ];

      // Try each pattern until we find a match
      for (const pattern of promptPatterns) {
        console.log("PromptOptimizerTool: Trying prompt pattern:", pattern);
        const promptMatch = response.match(pattern);
        if (promptMatch && promptMatch[1] && promptMatch[1].trim()) {
          optimizedPrompt = promptMatch[1].trim();
          console.log("PromptOptimizerTool: Found optimized prompt using pattern:", pattern);
          console.log("PromptOptimizerTool: Extracted prompt:", optimizedPrompt.substring(0, 100) + (optimizedPrompt.length > 100 ? "..." : ""));
          break;
        } else {
          console.log("PromptOptimizerTool: No match found with this pattern");
        }
      }

      // Try to extract the explanation with different patterns
      const explanationPatterns = [
        /EXPLANATION:\s*\n([\s\S]*?)(?:\n\s*$)/i,
        /EXPLANATION:\s*([\s\S]*?)(?:\n\s*$)/i,
        /\[Your explanation of changes here\]\s*\n([\s\S]*?)(?:\n\s*$)/i
      ];

      // Try each pattern until we find a match
      for (const pattern of explanationPatterns) {
        console.log("PromptOptimizerTool: Trying explanation pattern:", pattern);
        const explanationMatch = response.match(pattern);
        if (explanationMatch && explanationMatch[1] && explanationMatch[1].trim()) {
          explanation = explanationMatch[1].trim();
          console.log("PromptOptimizerTool: Found explanation using pattern:", pattern);
          console.log("PromptOptimizerTool: Extracted explanation:", explanation.substring(0, 100) + (explanation.length > 100 ? "..." : ""));
          break;
        } else {
          console.log("PromptOptimizerTool: No explanation match found with this pattern");
        }
      }
    }

    // If we couldn't extract the prompt using headers, try to extract it based on content
    if (!optimizedPrompt) {
      console.log("PromptOptimizerTool: No prompt found with headers, attempting content-based extraction");

      // Split the response into sections
      const sections = response.split(/\n\n+/);

      // First, look for sections that start with prompt-like phrases
      for (const section of sections) {
        const trimmedSection = section.trim();
        if (trimmedSection.length > 30 &&
            /^(provide|explain|describe|analyze|discuss|compare|list|outline|summarize|create|generate|write)/i.test(trimmedSection)) {
          // Check if this section contains meta-instructions (like "** assumes we have all the tools")
          if (!/\*\*.*\*\*|NOTE:|IMPORTANT:|ASSUMPTION:|CONTEXT:|BACKGROUND:|\[NOTE\]|\[IMPORTANT\]|\[CONTEXT\]|\[BACKGROUND\]/i.test(trimmedSection)) {
            optimizedPrompt = trimmedSection;
            console.log("PromptOptimizerTool: Extracted prompt from section:", optimizedPrompt.substring(0, 100) + "...");
            break;
          }
        }
      }

      // If still no prompt found, try to find the first paragraph that looks like a prompt
      if (!optimizedPrompt) {
        for (const section of sections) {
          const trimmedSection = section.trim();
          // Look for paragraphs that don't contain meta-instructions and are of reasonable length
          if (trimmedSection.length > 30 && trimmedSection.length < 800 &&
              !/\*\*.*\*\*|NOTE:|IMPORTANT:|ASSUMPTION:|CONTEXT:|BACKGROUND:|\[NOTE\]|\[IMPORTANT\]|\[CONTEXT\]|\[BACKGROUND\]/i.test(trimmedSection)) {
            optimizedPrompt = trimmedSection;
            console.log("PromptOptimizerTool: Using first reasonable paragraph as prompt:", optimizedPrompt.substring(0, 100) + "...");
            break;
          }
        }
      }

      // If still no prompt found, use the whole response but try to clean it
      if (!optimizedPrompt) {
        optimizedPrompt = response.trim();
        console.log("PromptOptimizerTool: Using whole response as prompt");
      }
    }

    // Clean up the optimized prompt to remove any remaining meta-instructions
    if (optimizedPrompt) {
      // Remove any lines that look like meta-instructions or notes
      const lines = optimizedPrompt.split('\n');
      const filteredLines = lines.filter(line => {
        const trimmedLine = line.trim();
        return !(
          /^\*\*.*\*\*$/.test(trimmedLine) || // **note**
          /^NOTE:|^IMPORTANT:|^ASSUMPTION:|^CONTEXT:|^BACKGROUND:/.test(trimmedLine) || // NOTE: something
          /^\[NOTE\]|\[IMPORTANT\]|\[CONTEXT\]|\[BACKGROUND\]/.test(trimmedLine) || // [NOTE] something
          /^The PMOAgent will|^Like the context|^Using an LLM/.test(trimmedLine) || // Specific to the example
          trimmedLine.startsWith('**') || // Lines starting with **
          /^\d+\.\s+(Marketing|Research|Software|Sales|Business)/.test(trimmedLine) // Numbered team list
        );
      });

      // Rejoin the filtered lines
      optimizedPrompt = filteredLines.join('\n').trim();

      // If the prompt starts with a list of teams or meta-instructions, try to find the actual prompt
      if (/^\d+\.\s+(Marketing|Research|Software|Sales|Business)/.test(optimizedPrompt)) {
        // Look for the first line that starts with a prompt-like phrase
        const lines = optimizedPrompt.split('\n');
        for (let i = 0; i < lines.length; i++) {
          if (/^(provide|explain|describe|analyze|discuss|compare|list|outline|summarize|create|generate|write)/i.test(lines[i].trim())) {
            // Use this line and all following lines
            optimizedPrompt = lines.slice(i).join('\n').trim();
            break;
          }
        }
      }
    }

    // Validate that the optimized prompt is actually a prompt and not an answer or meta-instructions
    if (optimizedPrompt) {
      // Check for common prompt verbs/phrases at the beginning
      const promptPatterns = [
        /^(provide|explain|describe|analyze|discuss|compare|list|outline|summarize|elaborate on|detail|write about|tell me about|give me|create|generate)/i,
        /^(what|who|when|where|why|how)/i,
        /^(can you|could you|would you|please)/i,
        /^(i need|i want|i would like|i am looking for)/i
      ];

      // If the optimized prompt doesn't start with a common prompt pattern,
      // check if it contains meta-instructions or team lists
      const isPromptFormat = promptPatterns.some(pattern => pattern.test(optimizedPrompt.trim()));
      const containsMetaInstructions = /\*\*.*\*\*|NOTE:|IMPORTANT:|ASSUMPTION:|CONTEXT:|BACKGROUND:|\[NOTE\]|\[IMPORTANT\]|\[CONTEXT\]|\[BACKGROUND\]|The PMOAgent will|Like the context|Using an LLM/i.test(optimizedPrompt);
      const containsTeamList = /\d+\.\s+(Marketing|Research|Software|Sales|Business)/i.test(optimizedPrompt);

      // Check if the text is too long (likely an answer rather than a prompt)
      const isTooLong = optimizedPrompt.length > 800;

      // Check for common answer patterns (paragraphs, bullet points, etc.)
      const hasAnswerPatterns = /(\n\n|\n-|\n\d+\.|In conclusion|To summarize|In summary)/i.test(optimizedPrompt);

      if (!isPromptFormat || isTooLong || hasAnswerPatterns || containsMetaInstructions || containsTeamList) {
        console.log("PromptOptimizerTool: Optimized text appears to be an answer or contains meta-instructions. Reformatting...");

        // Extract key terms from the text to create a better prompt
        const words = optimizedPrompt.split(/\s+/).filter(word => word.length > 3 && !/\*\*|\[|\]|NOTE:|IMPORTANT:|ASSUMPTION:|CONTEXT:|BACKGROUND:/.test(word));
        const keyTerms = [...new Set(words)].slice(0, 5).join(", ");

        // Create a clean, simple prompt
        optimizedPrompt = `Provide a detailed explanation about ${keyTerms} with historical context, key concepts, and specific examples. Use an informative tone suitable for a general audience.`;
      }
    }

    return { optimizedPrompt, explanation };
  }

  /**
   * Get the tool description for use by AI agents
   * @returns Tool description in a standardized format
   */
  getDescription(): typeof PromptOptimizerTool.description {
    return PromptOptimizerTool.description;
  }

  /**
   * Get all available tool methods with their descriptions
   * @returns Map of method names to their descriptions
   */
  getAvailableMethods(): Record<string, string> {
    return {
      optimizePrompt: "Optimize a prompt by applying a list of criteria"
    };
  }
}

// Export a singleton instance
export const promptOptimizerTool = new PromptOptimizerTool();
