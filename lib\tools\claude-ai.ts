/**
 * <PERSON> (Anthropic) integration for the LLM tool
 */

// Import the Anthropic SDK
import { anthropic } from "@ai-sdk/anthropic";
import { streamText, type Message } from "ai";

// Define interfaces for Claude processing
export interface ClaudeProcessingOptions {
  prompt: string;
  model?: string;
  modelOptions?: Record<string, any>;
}

/**
 * Process content with <PERSON> (Anthropic)
 * @param options - Processing options
 * @param options.prompt - The prompt to send to the LLM
 * @param options.model - The model to use (default: "claude-sonnet-4-0-latest")
 * @param options.modelOptions - Additional model-specific options
 * @returns The generated content
 * @throws Error if API key is missing or API call fails.
 */
export async function processWithClaude(options: ClaudeProcessingOptions): Promise<string> {
  try {
    const {
      prompt,
      model = "claude-sonnet-4-0-latest",
      modelOptions = {}
    } = options;

    // Get the API key from environment variables
    const anthropicApiKey = process.env.CLAUDE_API_KEY || '************************************************************************************************************';
    if (!anthropicApiKey) {
      console.error("FATAL: Anthropic API key (CLAUDE_API_KEY) not found in environment variables.");
      throw new Error("Anthropic API key is not configured. Please set the CLAUDE_API_KEY environment variable.");
    } else {
      // Avoid logging the full key; just a part of it for confirmation if needed.
      console.warn("Using Anthropic API key (first 10 chars):", anthropicApiKey.substring(0, 10) + "...");
    }

    console.warn(`🔍 DEBUGGING - CLAUDE API CALL 🔍`);
    console.warn(`Model: ${model}`);
    console.warn(`Temperature: ${modelOptions.temperature || 0.7}`);
    console.warn(`Max Tokens: ${modelOptions.maxTokens || 7000}`);
    console.warn(`Prompt Length: ${prompt.length} characters`);
    console.warn(`Prompt Preview: ${prompt.substring(0, 100)}...`);
    // console.warn("Full prompt length:", prompt.length); // Already logged
    // console.warn("Full prompt first 500 chars:", prompt.substring(0, 500)); // Potentially too verbose for regular logs

    // Initialize the Anthropic provider
    const modelProvider = anthropic(model, {
        apiKey: anthropicApiKey,
        // dangerouslyAllowBrowser should be true ONLY if this code is intended to run directly in the browser
        // AND you understand the security implications of exposing the API key or are using a restricted key.
        // For backend services, this is typically not needed or false.
        dangerouslyAllowBrowser: true // Assuming this is a deliberate choice for the project's architecture
    } as any); // 'as any' if type checking for dangerouslyAllowBrowser is problematic with specific SDK versions

    // Prepare messages array with optional system prompt
    const messages: Omit<Message, "id">[] = modelOptions.systemPrompt
      ? [
          { role: 'system', content: modelOptions.systemPrompt },
          { role: 'user', content: prompt }
        ]
      : [
          { role: 'user', content: prompt }
        ];

    // Use streamText to generate the response
    const { textStream } = await streamText({ // Added await here as streamText is async
      model: modelProvider,
      messages: messages,
      temperature: modelOptions.temperature || 0.7,
      maxTokens: modelOptions.maxTokens || 7000
    });

    console.warn("🔍 DEBUGGING - Starting to collect Claude response stream...");
    let result = "";
    let chunkCount = 0;

    try {
      for await (const delta of textStream) {
        result += delta;
        chunkCount++;
        if (chunkCount % 10 === 0) {
          console.warn(`Received ${chunkCount} chunks, current length: ${result.length} characters`);
        }
      }
      console.warn(`🔍 DEBUGGING - Claude response complete: ${chunkCount} total chunks, ${result.length} characters`);
      return result.trim();
    } catch (streamError: any) { // Explicitly type streamError
      console.error("Error while streaming response from Claude:", streamError);
      throw streamError; // Re-throw to be caught by the outer try/catch
    }
  } catch (error: any) { // Explicitly type error
    console.error("=== CLAUDE API ERROR ===");
    console.error("Error processing content with Claude:", error);

    const errorDetails = {
      message: error.message || "Unknown error",
      name: error.name,
      stack: error.stack, // Be cautious about logging full stack in production
      code: error.code,
      status: error.status,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      } : undefined
    };
    console.error("Error details:", JSON.stringify(errorDetails, null, 2));
    console.error("=== END CLAUDE API ERROR ===");

    try {
      if (typeof window !== 'undefined' && window.alert) {
        window.alert("DEBUG: Claude API Error: " + error.message);
      }
    } catch (e) {
      console.error("Could not show alert:", e);
    }
    // Throw an error object instead of returning an error string
    throw new Error(`Claude API Error: ${error.message || "Unknown error"}`);
  }
}

/**
 * Get available Claude models
 * @returns List of available models
 */
export function getClaudeModels(): string[] {
  return [
    // Claude 4 models
    "claude-sonnet-4-0",
    "claude-opus-4-0",

    // Claude 4.0 models
    "claude-sonnet-4-0-latest",
  ];
}